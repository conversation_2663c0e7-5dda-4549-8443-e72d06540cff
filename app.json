{"expo": {"name": "plc", "slug": "plc", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "plc", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-dev-client", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}]], "experiments": {"typedRoutes": true}}}