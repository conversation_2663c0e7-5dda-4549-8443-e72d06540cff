import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface DashboardStats {
  totalCourses: number;
  totalFlashcards: number;
  studyStreak: number;
  weeklyProgress: number;
}

export default function DashboardScreen() {
  const { user } = useUser();
  const [stats, setStats] = useState<DashboardStats>({
    totalCourses: 0,
    totalFlashcards: 0,
    studyStreak: 0,
    weeklyProgress: 0,
  });
  const [recentCourses, setRecentCourses] = useState([]);
  const [upcomingReviews, setUpcomingReviews] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      // Load stats
      const [coursesResult, flashcardsResult, sessionsResult] =
        await Promise.all([
          supabase.from("courses").select("id").eq("user_id", supabaseUser.id),
          supabase
            .from("flashcards")
            .select("id")
            .eq("user_id", supabaseUser.id),
          supabase
            .from("study_sessions")
            .select("completed_at")
            .eq("user_id", supabaseUser.id)
            .gte(
              "completed_at",
              new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
            ),
        ]);

      setStats({
        totalCourses: coursesResult.data?.length || 0,
        totalFlashcards: flashcardsResult.data?.length || 0,
        studyStreak: calculateStreak(sessionsResult.data || []),
        weeklyProgress: sessionsResult.data?.length || 0,
      });

      // Load recent courses
      const { data: courses } = await supabase
        .from("courses")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .order("created_at", { ascending: false })
        .limit(3);

      setRecentCourses(courses || []);

      // Load upcoming flashcard reviews
      const { data: reviews } = await supabase
        .from("flashcards")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .lte("next_review_date", new Date().toISOString())
        .limit(5);

      setUpcomingReviews(reviews || []);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
  };

  const calculateStreak = (sessions: any[]) => {
    // Simple streak calculation - count consecutive days with sessions
    const today = new Date();
    let streak = 0;

    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      const dateStr = checkDate.toDateString();

      const hasSession = sessions.some(
        (session) => new Date(session.completed_at).toDateString() === dateStr
      );

      if (hasSession) {
        streak++;
      } else if (i > 0) {
        break;
      }
    }

    return streak;
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.greeting}>
          Welcome back, {user?.firstName || "Learner"}!
        </Text>
        <Text style={styles.subtitle}>
          Ready to continue your learning journey?
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalCourses}</Text>
          <Text style={styles.statLabel}>Courses</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalFlashcards}</Text>
          <Text style={styles.statLabel}>Flashcards</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.studyStreak}</Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.weeklyProgress}</Text>
          <Text style={styles.statLabel}>This Week</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push("/upload-course")}
          >
            <Text style={styles.actionButtonText}>Upload Course</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => router.push("/(tabs)/flashcards")}
          >
            <Text style={styles.actionButtonText}>Review Cards</Text>
          </TouchableOpacity>
        </View>
      </View>

      {recentCourses.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Courses</Text>
          {recentCourses.map((course: any) => (
            <TouchableOpacity
              key={course.id}
              style={styles.courseCard}
              onPress={() => router.push(`/course/${course.id}`)}
            >
              <Text style={styles.courseTitle}>{course.title}</Text>
              <Text style={styles.courseDescription}>{course.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {upcomingReviews.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Due for Review</Text>
          <Text style={styles.reviewCount}>
            {upcomingReviews.length} flashcards ready for review
          </Text>
          <TouchableOpacity
            style={styles.reviewButton}
            onPress={() => router.push("/flashcard-review")}
          >
            <Text style={styles.reviewButtonText}>Start Review</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#007AFF",
  },
  greeting: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#fff",
    opacity: 0.9,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#fff",
    marginTop: -10,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statCard: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  section: {
    margin: 20,
    marginTop: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 0.48,
    alignItems: "center",
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  courseCard: {
    backgroundColor: "#fff",
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 5,
  },
  courseDescription: {
    fontSize: 14,
    color: "#666",
  },
  reviewCount: {
    fontSize: 16,
    color: "#666",
    marginBottom: 15,
  },
  reviewButton: {
    backgroundColor: "#34C759",
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: "center",
  },
  reviewButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
